<div class="min-h-screen support-container relative overflow-hidden">
  <!-- Background pattern para sa support page, friendly ug welcoming design -->
  <div class="absolute inset-0 opacity-6">
    <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
      <defs>
        <pattern id="support-grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1" fill="#3b82f6" opacity="0.3"/>
          <circle cx="5" cy="5" r="0.5" fill="#f59e0b" opacity="0.4"/>
          <circle cx="15" cy="15" r="0.5" fill="#0ea5e9" opacity="0.4"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#support-grid)" />
    </svg>
  </div>

  <!-- Enhanced Professional Header with Hover Text Labels -->
  <header class="bg-gray-950 shadow-xl relative z-10 w-full overflow-x-hidden">
    <div class="container mx-auto px-4 sm:px-6 py-2 sm:py-3 max-w-full">
      <!-- Main header row -->
      <div class="flex justify-between items-center w-full min-w-0">
        <!-- Logo section -->
        <div class="flex-shrink-0 flex justify-start items-center min-w-0">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-8 sm:h-10 md:h-12 lg:h-14 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Enhanced Navigation with Hover Text Labels -->
        <div class="flex items-center justify-end ml-auto space-x-1">
          <!-- Desktop Navigation - Hidden on mobile -->
          <div class="hidden md:flex items-center space-x-1">
            <strong class="text-gray-300 text-sm">LMS 2026</strong>
          </div>

          <!-- Desktop Navigation Links - Hidden on mobile -->
          <div class="hidden md:flex items-center space-x-1">
            <!-- Main Website -->
            <a href="https://benedictocollege.edu.ph" target="_blank" class="group relative flex items-center justify-center p-2 rounded-lg transition-all duration-300 hover:bg-white/10 cursor-pointer">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
              </svg>
              <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">Main Website</span>
            </a>

            <!-- Help & Support -->
            <a routerLink="/support" class="group relative flex items-center justify-center p-2 rounded-lg transition-all duration-300 hover:bg-white/10 cursor-pointer">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">Help & Support</span>
            </a>

            <!-- About Us -->
            <a routerLink="/about" class="group relative flex items-center justify-center p-2 rounded-lg transition-all duration-300 hover:bg-white/10 cursor-pointer">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">About Us</span>
            </a>

            <!-- Contact Us -->
            <a routerLink="/contact" class="group relative flex items-center justify-center p-2 rounded-lg transition-all duration-300 hover:bg-white/10 cursor-pointer">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">Contact Us</span>
            </a>

            <!-- My Account -->
            <a routerLink="/login" class="group relative flex items-center justify-center p-2 rounded-lg transition-all duration-300 hover:bg-white/10 cursor-pointer">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">My Account</span>
            </a>
          </div>

        <div id="mobile-burger" class="flex items-center">
          <button onclick="
            const navPane = document.getElementById('mobile-nav-pane');
            const burgerIcon = document.getElementById('burger-icon');
            const closeIcon = document.getElementById('close-icon');
            const overlay = document.getElementById('mobile-nav-overlay');

            if (navPane.classList.contains('translate-x-full')) {
              navPane.classList.remove('translate-x-full');
              overlay.classList.remove('hidden');
              overlay.classList.add('show');
              burgerIcon.classList.add('hidden');
              closeIcon.classList.remove('hidden');
              document.body.style.overflow = 'hidden';
            } else {
              navPane.classList.add('translate-x-full');
              overlay.classList.add('hidden');
              overlay.classList.remove('show');
              burgerIcon.classList.remove('hidden');
              closeIcon.classList.add('hidden');
              document.body.style.overflow = '';
            }
          " class="text-white focus:outline-none p-2 relative z-50">
            <svg id="burger-icon" class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
            </svg>
            <svg id="close-icon" class="w-6 h-6 hidden transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        </div>
      </div>
    </div>
  </header>

  <!-- Mobile Overlay for blur effect -->
  <div id="mobile-nav-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300 md:hidden" onclick="
    const pane = document.getElementById('mobile-nav-pane');
    const burger = document.getElementById('burger-icon');
    const close = document.getElementById('close-icon');
    const overlayEl = document.getElementById('mobile-nav-overlay');

    pane.classList.add('translate-x-full');
    overlayEl.classList.add('hidden');
    overlayEl.classList.remove('show');
    burger.classList.remove('hidden');
    close.classList.add('hidden');
    document.body.style.overflow = '';
  "></div>

  <!-- Mobile Sliding Navigation Pane -->
  <div id="mobile-nav-pane" class="fixed top-0 right-0 h-full w-80 bg-gray-900 transform translate-x-full transition-transform duration-300 ease-in-out z-50 md:hidden">
    <!-- Navigation Header -->
    <div class="bg-gray-950 border-b border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <img src="assets/images/BcLogo.png" alt="BC Logo" class="h-8 w-auto mr-3">
          <span class="text-white font-bold text-lg">Navigation</span>
        </div>
        <button id="close-nav-button" class="text-white hover:text-orange-400 transition duration-300" onclick="
          const closePane = document.getElementById('mobile-nav-pane');
          const closeBurger = document.getElementById('burger-icon');
          const closeX = document.getElementById('close-icon');
          const closeOverlay = document.getElementById('mobile-nav-overlay');

          closePane.classList.add('translate-x-full');
          closeOverlay.classList.add('hidden');
          closeOverlay.classList.remove('show');
          closeBurger.classList.remove('hidden');
          closeX.classList.add('hidden');
          document.body.style.overflow = '';
        ">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Navigation Content -->
    <div class="p-6 h-full overflow-y-auto">
      <!-- Main Navigation -->
      <div class="space-y-1 mb-6">
        <h3 class="text-gray-400 text-xs uppercase tracking-wider font-semibold mb-3">Main Navigation</h3>
        <a href="https://benedictocollege.edu.ph" target="_blank" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
          </svg>
          <span class="font-medium">Main Website</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
          </svg>
        </a>
        <a routerLink="/about" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">About Us</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
        <a routerLink="/contact" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
          <span class="font-medium">Contact Us</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
        <a routerLink="/support" class="nav-link flex items-center py-3 px-4 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-white group-hover:text-orange-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">Help & Support</span>
          <svg class="w-4 h-4 ml-auto text-white group-hover:text-orange-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>

      <!-- Login Section -->
      <div class="border-t border-gray-700 pt-6">
        <h3 class="text-gray-400 text-xs uppercase tracking-wider font-semibold mb-3">Account Access</h3>
        <a routerLink="/login" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group mb-3">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          <span class="font-medium">Student Login</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>

        <!-- Quick Access Portals -->
        <div class="space-y-2">
          <a routerLink="/faculty-login" class="nav-link flex items-center py-2 px-4 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition duration-300 text-sm">
            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
            Faculty Portal
          </a>
          <a routerLink="/admin-login" class="nav-link flex items-center py-2 px-4 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition duration-300 text-sm">
            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
            Admin Portal
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Main content section para sa support -->
  <main class="relative z-10 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      
      <!-- Support Header -->
      <div class="text-center mb-12">
        <div >
          <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
          </svg>
        </div>
        <h1 class="text-4xl sm:text-5xl font-bold support-header mb-4">Student Support Center</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          We're here to help you make the most of your library experience at Benedicto College. Find answers, get assistance, and access resources.
        </p>
        <div class="support-divider w-32 mx-auto mt-6"></div>
      </div>

      <!-- Emergency Contact -->
      <div class="max-w-4xl mx-auto">
        <div class="urgent-box p-6 mb-8">
          <div class="flex items-center mb-4">
            <svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <h3 class="text-xl font-bold text-gray-800">Priority Support Contact</h3>
          </div>
          <p class="text-gray-700 mb-4">For urgent technical issues or account-related concerns, please contact our support team through the following channels:</p>
          <div class="flex flex-col sm:flex-row gap-4">
            <a href="tel:+***********" class="bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 text-center">
              📞 Phone: (*************
            </a>
            <a href="mailto:support&#64;benedictocollege.edu.ph" class="bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 text-center">
              ✉️ Email: support&#64;benedictocollege.edu.ph
            </a>
          </div>
        </div>
           <hr class="border-gray-300 my-20 w-100" >

      </div>



      <!-- Search Section -->
      <div class="mb-12">
        <div class="bg-gray-50 py-8 px-6 rounded-lg border-l-4 border-blue-500">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Search Help & Support</h2>

          <!-- Search Bar -->
          <div class="max-w-2xl mx-auto mb-6">
            <div class="relative">
              <input
                type="text"
                id="supportSearch"
                placeholder="Search for help topics, guides, or enter your question..."
                class="w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                autocomplete="off"
              >
              <svg class="absolute left-4 top-3.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>

              <!-- Search Suggestions Dropdown -->
              <div id="searchSuggestions" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg mt-1 hidden z-50 max-h-60 overflow-y-auto">
                <!-- Suggestions will be populated here -->
              </div>
            </div>
          </div>

          <!-- Popular Searches -->
          <div class="text-center">
            <p class="text-gray-600 mb-3">Popular searches:</p>
            <div class="flex flex-wrap justify-center gap-2">
              <span onclick="searchAndScroll('password reset')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Password Reset</span>
              <span onclick="searchAndScroll('student id format')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Student ID Format</span>
              <span onclick="searchAndScroll('book renewal')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Book Renewal</span>
              <span onclick="searchAndScroll('study room')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Study Room</span>
              <span onclick="searchAndScroll('library hours')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Library Hours</span>
              <span onclick="searchAndScroll('overdue fines')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Overdue Fines</span>
              <span onclick="searchAndScroll('login problems')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Login Problems</span>
              <span onclick="searchAndScroll('book search')" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300 border border-gray-200">Book Search</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Help Categories -->
      <div class="space-y-8 mb-12">

        <!-- Account & Login Help -->
        <div id="account-login" class="border-l-4 border-orange-500 pl-6 py-4 transition-all duration-500">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Account & Login</h3>
          <p class="text-gray-600 mb-4">Issues with student ID, password reset, account access, and login problems.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Forgot password recovery</li>
            <li>• Student ID format help</li>
            <li>• Account activation</li>
            <li>• Login troubleshooting</li>
          </ul>
        </div>

        <!-- Library Resources -->
        <div id="library-resources" class="border-l-4 border-orange-500 pl-6 py-4 transition-all duration-500">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Library Resources</h3>
          <p class="text-gray-600 mb-4">Help with finding books, digital resources, and research materials.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Book search and reservation</li>
            <li>• Digital database access</li>
            <li>• Research assistance</li>
            <li>• Citation help</li>
          </ul>
        </div>

        <!-- Technical Support -->
        <div id="technical-support" class="border-l-4 border-orange-500 pl-6 py-4 transition-all duration-500">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Technical Support</h3>
          <p class="text-gray-600 mb-4">System issues, browser problems, and technical troubleshooting.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Browser compatibility</li>
            <li>• System performance</li>
            <li>• Mobile app support</li>
            <li>• Connection issues</li>
          </ul>
        </div>

        <!-- Borrowing & Returns -->
        <div id="borrowing-returns" class="border-l-4 border-orange-500 pl-6 py-4 transition-all duration-500">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Borrowing & Returns</h3>
          <p class="text-gray-600 mb-4">Questions about borrowing policies, due dates, and renewals.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Borrowing limits and policies</li>
            <li>• Renewal procedures</li>
            <li>• Overdue fines</li>
            <li>• Return procedures</li>
          </ul>
        </div>

        <!-- Study Spaces -->
        <div id="study-spaces" class="border-l-4 border-orange-500 pl-6 py-4 transition-all duration-500">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">Study Spaces</h3>
          <p class="text-gray-600 mb-4">Information about study rooms, computer labs, and facility reservations.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Room reservations</li>
            <li>• Computer lab access</li>
            <li>• Group study areas</li>
            <li>• Facility guidelines</li>
          </ul>
        </div>

        <!-- General Inquiries -->
        <div id="general-inquiries" class="border-l-4 border-orange-500 pl-6 py-4 transition-all duration-500">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">General Inquiries</h3>
          <p class="text-gray-600 mb-4">Other questions, feedback, and general library information.</p>
          <ul class="text-gray-700 space-y-2">
            <li>• Library hours and policies</li>
            <li>• Services and programs</li>
            <li>• Feedback and suggestions</li>
            <li>• General information</li>
          </ul>
        </div>

      </div>

      <!-- Contact Information -->
      <h2 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>

      <!-- Primary Support -->
      <h3 class="text-xl font-semibold text-gray-900 mb-4">Primary Support</h3>
      <p class="text-gray-700 mb-2"><span class="font-medium">Email:</span> support&#64;benedictocollege.edu.ph</p>
      <p class="text-gray-700 mb-2"><span class="font-medium">Phone:</span> (*************</p>
      <p class="text-gray-700 mb-8"><span class="font-medium">Hours:</span> Monday-Friday: 8:00 AM - 5:00 PM</p>

      <!-- Walk-in Support -->
      <h3 class="text-xl font-semibold text-gray-900 mb-4">Walk-in Support</h3>
      <p class="text-gray-700 mb-2"><span class="font-medium">Location:</span> Library Main Floor, Information Desk</p>
      <p class="text-gray-700 mb-2"><span class="font-medium">Hours:</span> Monday-Saturday: 7:00 AM - 8:00 PM</p>
      <p class="text-gray-700 mb-8"><span class="font-medium">Staff:</span> Librarians & IT Support Specialists</p>

      <!-- Quick Tips -->
      <h3 class="text-xl font-semibold text-gray-900 mb-4">Quick Tips for Faster Support</h3>
      <p class="text-gray-700 mb-2">• Have your Student ID ready when contacting support</p>
      <p class="text-gray-700 mb-2">• Describe the specific error message or issue you're experiencing</p>
      <p class="text-gray-700 mb-2">• Try clearing your browser cache before reporting technical issues</p>
      <p class="text-gray-700 mb-2">• Check your internet connection for access problems</p>
      <p class="text-gray-700 mb-8">• Include screenshots when reporting visual issues via email</p>

    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500 relative z-10 mt-12">
    <div class="container mx-auto px-4 sm:px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+***********" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px and beyond) -->
      <div class="hidden xl:block">
        <div class="flex justify-between items-start mb-8">
          <!-- Get in Touch Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(032) 345-5790</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <a href="https://facebook.com/benedictocollege" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Facebook</a>
            <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Website</a>
            <a href="tel:+***********" class="text-gray-400 hover:text-green-400 transition duration-300">Phone</a>
          </div>
        </div>
      </div>



      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll to Top Button -->
  <button
    id="scrollToTopBtn"
    onclick="scrollToTop()"
    class="fixed bottom-6 right-6 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 z-50 opacity-0 invisible"
    title="Scroll to top"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
    </svg>
  </button>
</div>

<script>
  // Search suggestions data para sa library management system
  const searchSuggestions = [
    // Account & Login related
    { text: 'password reset', category: 'account-login', description: 'How to reset your password' },
    { text: 'forgot password', category: 'account-login', description: 'Password recovery help' },
    { text: 'student id format', category: 'account-login', description: 'Correct Student ID format' },
    { text: 'login problems', category: 'account-login', description: 'Login troubleshooting' },
    { text: 'account activation', category: 'account-login', description: 'Activate new account' },
    { text: 'cant login', category: 'account-login', description: 'Login access issues' },

    // Library Resources
    { text: 'book search', category: 'library-resources', description: 'How to find books' },
    { text: 'book reservation', category: 'library-resources', description: 'Reserve books online' },
    { text: 'digital resources', category: 'library-resources', description: 'Access online databases' },
    { text: 'research help', category: 'library-resources', description: 'Research assistance' },
    { text: 'citation help', category: 'library-resources', description: 'Citation guidelines' },
    { text: 'ebooks', category: 'library-resources', description: 'Digital book access' },

    // Technical Support
    { text: 'browser issues', category: 'technical-support', description: 'Browser compatibility' },
    { text: 'slow website', category: 'technical-support', description: 'Performance issues' },
    { text: 'mobile app', category: 'technical-support', description: 'Mobile support' },
    { text: 'connection problems', category: 'technical-support', description: 'Internet connectivity' },
    { text: 'error messages', category: 'technical-support', description: 'System errors' },

    // Borrowing & Returns
    { text: 'book renewal', category: 'borrowing-returns', description: 'Renew borrowed books' },
    { text: 'overdue fines', category: 'borrowing-returns', description: 'Late return fees' },
    { text: 'borrowing limits', category: 'borrowing-returns', description: 'How many books can I borrow' },
    { text: 'return books', category: 'borrowing-returns', description: 'Where to return books' },
    { text: 'lost book', category: 'borrowing-returns', description: 'Lost book procedures' },
    { text: 'due dates', category: 'borrowing-returns', description: 'Check book due dates' },

    // Study Spaces
    { text: 'study room', category: 'study-spaces', description: 'Reserve study rooms' },
    { text: 'computer lab', category: 'study-spaces', description: 'Computer access' },
    { text: 'group study', category: 'study-spaces', description: 'Group study areas' },
    { text: 'room reservation', category: 'study-spaces', description: 'Book study spaces' },

    // General
    { text: 'library hours', category: 'general-inquiries', description: 'Operating hours' },
    { text: 'library policies', category: 'general-inquiries', description: 'Rules and policies' },
    { text: 'services', category: 'general-inquiries', description: 'Library services' },
    { text: 'feedback', category: 'general-inquiries', description: 'Submit feedback' }
  ];

  let currentSuggestionIndex = -1;

  document.addEventListener('DOMContentLoaded', function() {
    console.log('Support page loaded, initializing search...');

    const searchInput = document.getElementById('supportSearch');
    const suggestionsContainer = document.getElementById('searchSuggestions');

    console.log('Search input found:', !!searchInput);
    console.log('Suggestions container found:', !!suggestionsContainer);

    if (searchInput && suggestionsContainer) {
      // Search input event listener
      searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        console.log('Search term:', searchTerm);

        if (searchTerm.length > 0) {
          showSuggestions(searchTerm);
        } else {
          hideSuggestions();
        }
      });

      // Handle Enter key for search
      searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          const searchTerm = this.value.trim();
          console.log('Enter pressed, searching for:', searchTerm);
          if (searchTerm) {
            searchAndScroll(searchTerm);
          }
        } else if (e.key === 'Escape') {
          hideSuggestions();
        }
      });

      // Hide suggestions when clicking outside
      document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
          hideSuggestions();
        }
      });
    } else {
      console.error('Search elements not found!');
    }
  });

  // Show search suggestions
  function showSuggestions(searchTerm) {
    console.log('Showing suggestions for:', searchTerm);

    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (!suggestionsContainer) {
      console.error('Suggestions container not found');
      return;
    }

    const filteredSuggestions = searchSuggestions.filter(suggestion =>
      suggestion.text.toLowerCase().includes(searchTerm) ||
      suggestion.description.toLowerCase().includes(searchTerm)
    ).slice(0, 8); // Limit to 8 suggestions

    console.log('Filtered suggestions:', filteredSuggestions.length);

    if (filteredSuggestions.length > 0) {
      const suggestionsHTML = filteredSuggestions.map((suggestion, index) => {
        return `
          <div class="suggestion-item px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
               data-search="${suggestion.text}"
               data-category="${suggestion.category}"
               onclick="searchAndScroll('${suggestion.text}')">
            <div class="flex items-center">
              <svg class="w-4 h-4 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              <div>
                <div class="text-sm font-medium text-gray-900">${suggestion.text}</div>
                <div class="text-xs text-gray-500">${suggestion.description}</div>
              </div>
            </div>
          </div>
        `;
      }).join('');

      suggestionsContainer.innerHTML = suggestionsHTML;
      suggestionsContainer.classList.remove('hidden');
      console.log('Suggestions displayed');
    } else {
      hideSuggestions();
    }
  }

  // Hide suggestions
  function hideSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    suggestionsContainer.classList.add('hidden');
    currentSuggestionIndex = -1;
  }

  // Update suggestion highlight
  function updateSuggestionHighlight(suggestions) {
    suggestions.forEach((suggestion, index) => {
      if (index === currentSuggestionIndex) {
        suggestion.classList.add('bg-blue-50');
      } else {
        suggestion.classList.remove('bg-blue-50');
      }
    });
  }

  // Search and scroll to relevant section
  function searchAndScroll(searchTerm) {
    console.log('searchAndScroll called with:', searchTerm);

    const searchInput = document.getElementById('supportSearch');
    if (searchInput) {
      searchInput.value = searchTerm;
    }
    hideSuggestions();

    // Find matching suggestion to get category
    const matchingSuggestion = searchSuggestions.find(suggestion =>
      suggestion.text.toLowerCase() === searchTerm.toLowerCase()
    );

    console.log('Matching suggestion found:', !!matchingSuggestion);

    if (matchingSuggestion) {
      console.log('Scrolling to category:', matchingSuggestion.category);
      scrollToSection(matchingSuggestion.category);
    } else {
      console.log('No direct match, searching in content');
      // Fallback: search by keywords in content
      searchInContent(searchTerm);
    }
  }

  // Scroll to specific section with highlight effect
  function scrollToSection(sectionId) {
    console.log('scrollToSection called with:', sectionId);

    const section = document.getElementById(sectionId);
    console.log('Section found:', !!section);

    if (section) {
      // Remove previous highlights
      document.querySelectorAll('.highlight-section').forEach(el => {
        el.classList.remove('highlight-section');
      });

      // Add highlight to target section
      section.classList.add('highlight-section');
      console.log('Highlight added to section');

      // Scroll to section with offset for header
      const headerHeight = 150;
      const sectionTop = section.offsetTop - headerHeight;

      console.log('Scrolling to position:', sectionTop);

      window.scrollTo({
        top: sectionTop,
        behavior: 'smooth'
      });

      // Remove highlight after animation
      setTimeout(() => {
        section.classList.remove('highlight-section');
        console.log('Highlight removed');
      }, 3000);
    } else {
      console.error('Section not found:', sectionId);
    }
  }

  // Search in content when no direct match found
  function searchInContent(searchTerm) {
    const sections = document.querySelectorAll('[id^="account-"], [id^="library-"], [id^="technical-"], [id^="borrowing-"], [id^="study-"], [id^="general-"]');
    const searchTermLower = searchTerm.toLowerCase();

    for (const section of sections) {
      const sectionText = section.textContent.toLowerCase();
      if (sectionText.includes(searchTermLower)) {
        scrollToSection(section.id);
        break;
      }
    }
  }

  // Scroll to Top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  // Show/hide scroll to top button based on scroll position
  window.addEventListener('scroll', function() {
    const scrollToTopBtn = document.getElementById('scrollToTopBtn');
    if (window.pageYOffset > 300) {
      scrollToTopBtn.classList.remove('opacity-0', 'invisible');
      scrollToTopBtn.classList.add('opacity-100', 'visible');
    } else {
      scrollToTopBtn.classList.remove('opacity-100', 'visible');
      scrollToTopBtn.classList.add('opacity-0', 'invisible');
    }
  });

  // ===== MOBILE NAVIGATION FUNCTIONALITY =====
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileNavPane = document.getElementById('mobile-nav-pane');
    const closeNavButton = document.getElementById('close-nav-button');
    const hamburgerIcon = document.getElementById('hamburger-icon');
    const closeIcon = document.getElementById('close-icon');
    const navLinks = document.getElementById('nav-links');

    // Toggle mobile navigation
    function toggleMobileNav() {
      const isOpen = !mobileNavPane.classList.contains('translate-x-full');

      if (isOpen) {
        // Close navigation
        mobileNavPane.classList.add('translate-x-full');
        navLinks.classList.remove('mobile-open');
        hamburgerIcon.classList.remove('hidden');
        closeIcon.classList.remove('show');
        document.body.classList.remove('overflow-hidden');
      } else {
        // Open navigation
        mobileNavPane.classList.remove('translate-x-full');
        navLinks.classList.add('mobile-open');
        hamburgerIcon.classList.add('hidden');
        closeIcon.classList.add('show');
        document.body.classList.add('overflow-hidden');
      }
    }

    // Event listeners
    if (mobileMenuButton) {
      mobileMenuButton.addEventListener('click', toggleMobileNav);
    }

    if (closeNavButton) {
      closeNavButton.addEventListener('click', toggleMobileNav);
    }

    // Close navigation when clicking outside
    document.addEventListener('click', function(event) {
      const isClickInsideNav = mobileNavPane.contains(event.target);
      const isClickOnButton = mobileMenuButton.contains(event.target);
      const isNavOpen = !mobileNavPane.classList.contains('translate-x-full');

      if (isNavOpen && !isClickInsideNav && !isClickOnButton) {
        toggleMobileNav();
      }
    });

    // Mobile navigation event handlers
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        const navPane = document.getElementById('mobile-nav-pane');
        const overlay = document.getElementById('mobile-nav-overlay');
        const burgerIcon = document.getElementById('burger-icon');
        const closeIcon = document.getElementById('close-icon');

        if (navPane && !navPane.classList.contains('translate-x-full')) {
          navPane.classList.add('translate-x-full');
          overlay.classList.add('hidden');
          overlay.classList.remove('show');
          burgerIcon.classList.remove('hidden');
          closeIcon.classList.add('hidden');
          document.body.style.overflow = '';
        }
      }
    });

    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768) {
        const navPane = document.getElementById('mobile-nav-pane');
        const overlay = document.getElementById('mobile-nav-overlay');
        const burgerIcon = document.getElementById('burger-icon');
        const closeIcon = document.getElementById('close-icon');

        if (navPane) {
          navPane.classList.add('translate-x-full');
          overlay.classList.add('hidden');
          overlay.classList.remove('show');
          burgerIcon.classList.remove('hidden');
          closeIcon.classList.add('hidden');
          document.body.style.overflow = '';
        }
      }
    });
  });
</script>
