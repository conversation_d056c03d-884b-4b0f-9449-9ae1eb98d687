<div class="min-h-screen bg-white text-gray-800 flex flex-col">
  
  <!-- Enhanced Professional Header with Hover Text Labels -->
  <header class="bg-gray-950 shadow-xl relative z-10 w-full overflow-x-hidden">
    <div class="container mx-auto px-4 sm:px-6 py-2 sm:py-3 max-w-full">
      <!-- Main header row -->
      <div class="flex justify-between items-center w-full min-w-0">
        <!-- Logo section -->
        <div class="flex-shrink-0 flex justify-start items-center min-w-0">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-8 sm:h-10 md:h-12 lg:h-14 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Enhanced Navigation with Hover Text Labels -->
        <div class="flex items-center justify-end ml-auto space-x-1">
          <!-- Desktop Navigation - Hidden on mobile -->
          <div class="hidden md:flex items-center space-x-1">
            <strong class="text-gray-300 text-sm">LMS 2026</strong>
          </div>

          <!-- Main Website -->
          <a href="https://benedictocollege.edu.ph" target="_blank" class="group flex items-center text-white hover:text-orange-400 transition-all duration-300 p-2 rounded-lg hover:bg-gray-800/20">
            <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="#ffffff" stroke-width="2" viewBox="0 0 24 24" style="transition: stroke 0.3s ease;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
            </svg>
            <span>Main Website</span>
          </a>

          <!-- Help & Support -->
          <a routerLink="/support" class="group flex items-center text-white hover:text-orange-400 transition-all duration-300 p-2 rounded-lg hover:bg-gray-800/20">
            <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="#ffffff" stroke-width="2" viewBox="0 0 24 24" style="transition: stroke 0.3s ease;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>Help & Support</span>
          </a>

          <!-- About Us -->
          <a routerLink="/about" class="group flex items-center text-white hover:text-orange-400 transition-all duration-300 p-2 rounded-lg hover:bg-gray-800/20">
            <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="#ffffff" stroke-width="2" viewBox="0 0 24 24" style="transition: stroke 0.3s ease;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>About Us</span>
          </a>

          <!-- Contact Us -->
          <a routerLink="/contact" class="group flex items-center text-white hover:text-orange-400 transition-all duration-300 p-2 rounded-lg hover:bg-gray-800/20">
            <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="#ffffff" stroke-width="2" viewBox="0 0 24 24" style="transition: stroke 0.3s ease;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span>Contact Us</span>
          </a>

          <!-- My Account -->
          <a routerLink="/login" class="group flex items-center text-white hover:text-orange-400 transition-all duration-300 p-2 rounded-lg hover:bg-gray-800/20">
            <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="#ffffff" stroke-width="2" viewBox="0 0 24 24" style="transition: stroke 0.3s ease;">
              <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span>My Account</span>
          </a>

        <div id="mobile-burger" class="flex items-center">
          <button onclick="
            const navPane = document.getElementById('mobile-nav-pane');
            const burgerIcon = document.getElementById('burger-icon');
            const closeIcon = document.getElementById('close-icon');
            const overlay = document.getElementById('mobile-nav-overlay');

            if (navPane.classList.contains('translate-x-full')) {
              navPane.classList.remove('translate-x-full');
              overlay.classList.remove('hidden');
              overlay.classList.add('show');
              burgerIcon.classList.add('hidden');
              closeIcon.classList.remove('hidden');
              document.body.style.overflow = 'hidden';
            } else {
              navPane.classList.add('translate-x-full');
              overlay.classList.add('hidden');
              overlay.classList.remove('show');
              burgerIcon.classList.remove('hidden');
              closeIcon.classList.add('hidden');
              document.body.style.overflow = '';
            }
          " class="text-white focus:outline-none p-2 relative z-50">
            <svg id="burger-icon" class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
            </svg>
            <svg id="close-icon" class="w-6 h-6 hidden transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        </div>
      </div>
    </div>
  </header>

  <!-- Mobile Overlay for blur effect -->
  <div id="mobile-nav-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300 md:hidden" onclick="
    const pane = document.getElementById('mobile-nav-pane');
    const burger = document.getElementById('burger-icon');
    const close = document.getElementById('close-icon');
    const overlayEl = document.getElementById('mobile-nav-overlay');

    pane.classList.add('translate-x-full');
    overlayEl.classList.add('hidden');
    overlayEl.classList.remove('show');
    burger.classList.remove('hidden');
    close.classList.add('hidden');
    document.body.style.overflow = '';
  "></div>

  <!-- Mobile Sliding Navigation Pane -->
  <div id="mobile-nav-pane" class="fixed top-0 right-0 h-full w-80 bg-gray-900 transform translate-x-full transition-transform duration-300 ease-in-out z-50 md:hidden">
    <!-- Navigation Header -->
    <div class="bg-gray-950 border-b border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <img src="assets/images/BcLogo.png" alt="BC Logo" class="h-8 w-auto mr-3">
          <span class="text-white font-bold text-lg">Navigation</span>
        </div>
        <button id="close-nav-button" class="text-white hover:text-orange-400 transition duration-300" onclick="
          const closePane = document.getElementById('mobile-nav-pane');
          const closeBurger = document.getElementById('burger-icon');
          const closeX = document.getElementById('close-icon');
          const closeOverlay = document.getElementById('mobile-nav-overlay');

          closePane.classList.add('translate-x-full');
          closeOverlay.classList.add('hidden');
          closeOverlay.classList.remove('show');
          closeBurger.classList.remove('hidden');
          closeX.classList.add('hidden');
          document.body.style.overflow = '';
        ">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Navigation Content -->
    <div class="p-6 h-full overflow-y-auto">
      <!-- Main Navigation -->
      <div class="space-y-1 mb-6">
        <h3 class="text-gray-400 text-xs uppercase tracking-wider font-semibold mb-3">Main Navigation</h3>
        <a href="https://benedictocollege.edu.ph" target="_blank" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
          </svg>
          <span class="font-medium">Main Website</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
          </svg>
        </a>
        <a routerLink="/about" class="nav-link flex items-center py-3 px-4 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-white group-hover:text-orange-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">About Us</span>
          <svg class="w-4 h-4 ml-auto text-white group-hover:text-orange-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
        <a routerLink="/contact" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
          <span class="font-medium">Contact Us</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
        <a routerLink="/support" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">Help & Support</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>

      <!-- Login Section -->
      <div class="border-t border-gray-700 pt-6">
        <h3 class="text-gray-400 text-xs uppercase tracking-wider font-semibold mb-3">Account Access</h3>
        <a routerLink="/login" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group mb-3">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          <span class="font-medium">Student Login</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>

        <!-- Quick Access Portals -->
        <div class="space-y-2">
          <a routerLink="/faculty-login" class="nav-link flex items-center py-2 px-4 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition duration-300 text-sm">
            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
            Faculty Portal
          </a>
          <a routerLink="/admin-login" class="nav-link flex items-center py-2 px-4 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition duration-300 text-sm">
            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
            Admin Portal
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <main class="flex-1 py-16 px-4 sm:px-6">
    <div class="about-container">
      
      <!-- Hero Section with Video Background -->
      <div class="hero-section relative overflow-hidden">
        <!-- Animated Background with Stock Image -->
        <div class="absolute inset-0 z-0">
          <!-- Background image with animation  stacked photo url https://images.pexels.com/photos/1595391/pexels-photo-1595391.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop--> 
          <div class="hero-background-image w-full h-full bg-cover bg-center bg-no-repeat opacity-40"
               style="background-image: url('assets/images/campus-life.jpg');">
          </div>
          <!-- Animated overlay -->
          <div class="absolute inset-0 bg-gradient-to-r from-gray-900/80 to-gray-800/60 animate-pulse-slow"></div>
          <!-- Floating particles animation -->
          <div class="absolute inset-0 overflow-hidden">
            <div class="floating-particle particle-1"></div>
            <div class="floating-particle particle-2"></div>
            <div class="floating-particle particle-3"></div>
            <div class="floating-particle particle-4"></div>
            <div class="floating-particle particle-5"></div>
          </div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-10">
          <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in-up">About Benedicto College</h1>
          <p class="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in-up animation-delay-300">
            Empowering minds, shaping futures through innovative education and technology
          </p>
          <div class="flex items-center justify-center animate-fade-in-up animation-delay-600">
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-blue-400 rounded-full animate-pulse"></div>
          </div>

          <!-- Call to Action Buttons -->
          <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up animation-delay-900">
            <a href="https://benedictocollege.edu.ph/facilities/index.html" target="_blank" class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition duration-300 transform hover:scale-105 shadow-lg text-center">
              Explore Our Campus
            </a>
            <a href="https://www.youtube.com/watch?v=ZmoqrRNXvnM&t=230s" target="_blank" class="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-semibold transition duration-300 transform hover:scale-105 text-center">
              Virtual Tour
            </a>
          </div>
        </div>
      </div>

 
    <div class="flex flex-col items-center space-y-1">
  <div class="text-center">
    <a href="#campus-life" class="text-black hover:text-white" onclick="document.getElementById('campus-life').scrollIntoView({behavior: 'smooth', block: 'start'}); return false;">
      <svg class="w-8 h-8 animate-bounce drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </a>
  </div>
</div>

      <!-- Campus Life Visual Section -->
      <div id="campus-life" class="campus-visual-section mb-16">
        <h2  id="campus-life" class="text-4xl font-bold text-center text-gray-900 mb-12">Campus Life at Benedicto College</h2>

        <!-- Main Campus Video -->
        <div id="campus-life"  class="campus-video mb-12">
          <img src="assets/images/campus-life-2.jpg"
               alt="Students studying in modern library"
               class="w-full h-96 object-cover rounded-2xl shadow-2xl">
        </div>

        <!-- Campus Activities Grid -->
        <div class="campus-grid grid md:grid-cols-3 gap-8 mb-12">
          <div class="campus-item">
            <img src="assets/images/collaborative-learning-2.png"
                 alt="Students collaborating in study groups"
                 class="w-full h-64 object-cover rounded-xl shadow-lg mb-4">
            <h3 class="text-xl font-bold text-gray-900 mb-2">Collaborative Learning</h3>
            <p class="text-gray-600">Students engage in dynamic group discussions and collaborative projects.</p>
          </div>

          <div class="campus-item">
            <img src="assets/images/smart-classrooms.png"
                 alt="Modern classroom with technology"
                 class="w-full h-64 object-cover rounded-xl shadow-lg mb-4">
            <h3 class="text-xl font-bold text-gray-900 mb-2">Smart Classrooms</h3>
            <p class="text-gray-600">State-of-the-art technology enhances the learning experience.</p>
          </div>

          <div class="campus-item">
            <img src="assets/images/research-excellence-2.png"
                 alt="Students in library research area"
                 class="w-full h-64 object-cover rounded-xl shadow-lg mb-4">
            <h3 class="text-xl font-bold text-gray-900 mb-2">Research Excellence</h3>
            <p class="text-gray-600">Comprehensive research facilities support academic achievement.</p>
          </div>
        </div>


        <!-- Student Activities -->
        <div class="student-activities grid md:grid-cols-2 gap-12 mb-12">
          <div class="activity-content">
            <h3 class="text-3xl font-bold text-gray-900 mb-6">Student Life & Activities</h3>
            <p class="text-lg text-gray-700 mb-6 leading-relaxed">
              Experience vibrant campus life with diverse extracurricular activities, student organizations, and cultural events that foster personal growth and community engagement.
            </p>
            <ul class="text-gray-700 space-y-3">
              <li class="flex items-center">
                <span class="w-2 h-2 bg-orange-500 rounded-full mr-3"></span>
                Student Government and Leadership Programs
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Cultural and Arts Organizations
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Sports and E-Sports Activities
              </li>
              <li class="flex items-center">
                <span class="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                Academic and Professional Clubs
              </li>
            </ul>
          </div>
          <div class="activity-image">
            <img src="assets/images/Esports.jpg"
                 alt="Students participating in campus activities"
                 class="w-full h-80 object-cover rounded-xl shadow-lg">
          </div>
        </div>

        <!-- Academic Excellence -->
        <div class="academic-excellence grid md:grid-cols-2 gap-12">
          <div class="excellence-image">
            <img src="assets/images/academic-excellence.jpg"
                 alt="Graduation ceremony at Benedicto College"
                 class="w-full h-80 object-cover rounded-xl shadow-lg">
          </div>
          <div class="excellence-content">
            <h3 class="text-3xl font-bold text-gray-900 mb-6">Academic Excellence</h3>
            <p class="text-lg text-gray-700 mb-6 leading-relaxed">
              Our commitment to academic excellence is reflected in our innovative programs, distinguished faculty, and comprehensive support systems that prepare students for success in their chosen fields.
            </p>
            <div class="stats-grid grid grid-cols-2 gap-6">
              <div class="stat-item text-center">
                <div class="text-3xl font-bold text-orange-600 mb-2">95%</div>
                <div class="text-gray-600">Graduate Employment Rate</div>
              </div>
              <div class="stat-item text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">40+</div>
                <div class="text-gray-600">Academic Programs</div>
              </div>
              <div class="stat-item text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">15:1</div>
                <div class="text-gray-600">Student-Faculty Ratio</div>
              </div>
              <div class="stat-item text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">25+</div>
                <div class="text-gray-600">Years of Excellence</div>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- Leadership Team Section -->
      <div class="leadership-section mb-16">
        <h2 class="text-4xl font-bold text-center text-gray-900 mb-4">Our Leadership Team</h2>
        <p class="text-center text-gray-600 mb-12 max-w-3xl mx-auto">
          Meet the verified leadership professionals who guide Benedicto College towards excellence in education and community service.
        </p>
        <div class="leadership-grid">
          <div class="leader-card">
            <div class="leader-avatar">
              <img src="assets/images/francisco-benedicto.jpg" alt="Ambassador Francisco L. Benedicto" class="w-full h-full object-cover rounded-full">
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Ambassador Francisco L. Benedicto</h3>
            <p class="text-orange-600 font-semibold mb-3">Founder & Chairman of the Board</p>
            <p class="text-gray-600 text-sm">
              Distinguished diplomat and educator, former Ambassador to China, Singapore, Korea, Brazil, Canada, and India. Multi-awarded leader in government service and education.
            </p>
          </div>

          <div class="leader-card">
            <div class="leader-avatar">
              <img src="assets/images/ranulfo-visaya.jpg" alt="Ranulfo L. Visaya Jr." class="w-full h-full object-cover rounded-full">
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Ranulfo L. Visaya Jr., LPT, M.Ed.</h3>
            <p class="text-blue-600 font-semibold mb-3">School Director</p>
            <p class="text-gray-600 text-sm">
              Licensed Professional Teacher with Master's in Education, leading academic excellence initiatives and institutional development programs.
            </p>
          </div>

          <div class="leader-card">
            <div class="leader-avatar">
              <!-- Replace with: <img src="assets/images/kenneth-huan.jpg" alt="Engr. Kenneth Huan" class="w-full h-full object-cover rounded-full"> -->
              <span>KH</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Engr. Kenneth Huan</h3>
            <p class="text-orange-600 font-semibold mb-3">Vice President for Administration</p>
            <p class="text-gray-600 text-sm">
              Professional Engineer overseeing administrative operations, facilities management, and institutional strategic planning across both campuses.
            </p>
          </div>

          <div class="leader-card">
            <div class="leader-avatar">
              <!-- Replace with: <img src="assets/images/joey-cortes.jpg" alt="Ms. Joey Cortes" class="w-full h-full object-cover rounded-full"> -->
              <span>JC</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Ms. Joey Cortes</h3>
            <p class="text-blue-600 font-semibold mb-3">BCTVS Administrator</p>
            <p class="text-gray-600 text-sm">
              Administrator of Benedicto College Technical Vocational School, leading TESDA-accredited programs and mobile training bus initiatives.
            </p>
          </div>

          <div class="leader-card">
            <div class="leader-avatar">
              <img src="assets/images/logo-collage.png" alt="Academic Leadership Team" class="w-full h-full object-cover rounded-full">
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Academic Leadership Team</h3>
            <p class="text-orange-600 font-semibold mb-3">College Deans & Directors</p>
            <p class="text-gray-600 text-sm">
              Dedicated deans from our six colleges: Arts & Sciences, Business & Management, Computer Studies, Education, Hospitality Management, and Engineering.
            </p>
          </div>

          <div class="leader-card">
            <div class="leader-avatar">
              <img src="assets/images/faculty.jpg" alt="Faculty & Staff" class="w-full h-full object-cover rounded-full">
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Faculty & Staff</h3>
            <p class="text-blue-600 font-semibold mb-3">Professional Educators</p>
            <p class="text-gray-600 text-sm">
              Committed faculty members and staff supporting student success across higher education, senior high school, basic education, and technical-vocational programs.
            </p>
          </div>
        </div>
      </div>

      <!-- Student Testimonials Section -->
      <div class="testimonials-section mb-16 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8">
        <h2 class="text-4xl font-bold text-center text-gray-900 mb-4">What Our Students Say</h2>
        <p class="text-center text-gray-600 mb-12">
          Hear from our students about their experience with our library management system.
        </p>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div class="testimonial-card bg-white p-6 rounded-xl shadow-lg">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                S
              </div>
              <div>
                <h4 class="font-bold text-gray-900">Sarah Chen</h4>
                <p class="text-sm text-gray-600">Computer Science, 3rd Year</p>
              </div>
            </div>
            <p class="text-gray-700 italic">
              "The new library system is amazing! I can easily find and reserve books online. The digital resources are incredibly helpful for my research."
            </p>
            <div class="flex text-yellow-400 mt-4">
              <span>★★★★★</span>
            </div>
          </div>

          <div class="testimonial-card bg-white p-6 rounded-xl shadow-lg">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                M
              </div>
              <div>
                <h4 class="font-bold text-gray-900">Mark Rodriguez</h4>
                <p class="text-sm text-gray-600">Business Administration, 2nd Year</p>
              </div>
            </div>
            <p class="text-gray-700 italic">
              "The mobile app makes it so convenient to check due dates and renew books. The notification system keeps me organized!"
            </p>
            <div class="flex text-yellow-400 mt-4">
              <span>★★★★★</span>
            </div>
          </div>

          <div class="testimonial-card bg-white p-6 rounded-xl shadow-lg">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                L
              </div>
              <div>
                <h4 class="font-bold text-gray-900">Lisa Garcia</h4>
                <p class="text-sm text-gray-600">Education, 4th Year</p>
              </div>
            </div>
            <p class="text-gray-700 italic">
              "As a graduating student, the research tools and database access have been invaluable for my thesis. Highly recommend!"
            </p>
            <div class="flex text-yellow-400 mt-4">
              <span>★★★★★</span>
            </div>
          </div>
        </div>
      </div>



      <!-- Accreditations & Government Partnerships Section -->
      <div class="accreditations-section mb-16">
        <h2 class="text-4xl font-bold text-center text-gray-900 mb-12">Official Accreditations & Government Partnerships</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="accreditation-card text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">CHED Recognized</h3>
            <p class="text-sm text-gray-600">Commission on Higher Education Philippines</p>
          </div>

          <div class="accreditation-card text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">TESDA Accredited</h3>
            <p class="text-sm text-gray-600">Technical Education & Skills Development Authority</p>
          </div>

          <div class="accreditation-card text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">LGU Partnerships</h3>
            <p class="text-sm text-gray-600">Local Government Units across Cebu Province</p>
          </div>

          <div class="accreditation-card text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V8m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v2"></path>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">DOLE Partnership</h3>
            <p class="text-sm text-gray-600">Department of Labor & Employment</p>
          </div>
        </div>

        <!-- Training & Assessment Centers -->
        <div class="mt-12 bg-gradient-to-r from-blue-50 to-orange-50 p-8 rounded-2xl">
          <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">TESDA Accredited Training & Assessment Centers</h3>
          <div class="grid md:grid-cols-2 gap-8">
            <div>
              <h4 class="font-bold text-lg text-blue-700 mb-4">Training Center Programs:</h4>
              <ul class="space-y-2 text-gray-700">
                <li>• Computer System Servicing NC II</li>
                <li>• Shielded Metal Arc Welding (SMAW NC I & II)</li>
                <li>• Cookery NC II & Bread and Pastry NC II</li>
                <li>• Bartending NC II & Food & Beverage Services NC II</li>
                <li>• Housekeeping NC II</li>
                <li>• Electrical Installation & Maintenance NC II</li>
                <li>• Plumbing NC II & Automotive NC II</li>
              </ul>
            </div>
            <div>
              <h4 class="font-bold text-lg text-orange-700 mb-4">Assessment Center Programs:</h4>
              <ul class="space-y-2 text-gray-700">
                <li>• Cookery NC II Assessment</li>
                <li>• SMAW NC II Assessment</li>
                <li>• Computer System Servicing NC II Assessment</li>
                <li>• Plumbing NC II Assessment</li>
              </ul>
              <div class="mt-6 p-4 bg-white rounded-lg">
                <p class="text-sm text-gray-600 italic">"If you cannot come to us, we will go to you!" - BCTVS Mobile Training Bus Program</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Community Impact Section -->
      <div class="stats-section">
        <h2 class="text-3xl font-bold text-center text-gray-900 mb-8">Our Community Impact</h2>
        <div class="grid md:grid-cols-4 gap-6">
          <div class="stat-item">
            <span class="stat-number animate-count-up" data-target="2000">0</span>
            <p class="text-gray-600 font-semibold">TESDA Graduates Trained</p>
          </div>
          <div class="stat-item">
            <span class="stat-number animate-count-up animation-delay-300" data-target="6">0</span>
            <p class="text-gray-600 font-semibold">Academic Colleges</p>
          </div>
          <div class="stat-item">
            <span class="stat-number animate-count-up animation-delay-600" data-target="25">0</span>
            <p class="text-gray-600 font-semibold">Years of Excellence</p>
          </div>
          <div class="stat-item">
            <span class="stat-number animate-count-up animation-delay-900" data-target="2">0</span>
            <p class="text-gray-600 font-semibold">Campus Locations</p>
          </div>
        </div>

        <!-- Mobile Training Bus Impact -->
        <div class="mt-12 bg-gradient-to-r from-orange-50 to-blue-50 p-8 rounded-2xl">
          <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">Mobile Training Bus Program</h3>
          <div class="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <div class="text-3xl font-bold text-orange-600 mb-2">7+</div>
              <p class="text-gray-700">Municipalities Reached</p>
              <p class="text-sm text-gray-600 mt-1">Carcar, Barili, Boljoon, San Remegio, Asturias, Mandaue</p>
            </div>
            <div>
              <div class="text-3xl font-bold text-blue-600 mb-2">10+</div>
              <p class="text-gray-700">TESDA Accredited Courses</p>
              <p class="text-sm text-gray-600 mt-1">Cookery, Welding, Computer Servicing, Automotive</p>
            </div>
            <div>
              <div class="text-3xl font-bold text-green-600 mb-2">100+</div>
              <p class="text-gray-700">Expected 2017 Graduates</p>
              <p class="text-sm text-gray-600 mt-1">Skills training for employment & entrepreneurship</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500">
    <div class="container mx-auto px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm">Benedicto College Main Campus, Philippines</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm">info&#64;benedictocollege.edu.ph</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span class="text-sm">(032) 345-5790</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+63321234567" class="bg-gray-800 hover:bg-orange-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px+) -->
      <div class="hidden xl:flex justify-between items-start mb-8">
        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
          <div class="space-y-3 text-gray-300">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="text-sm">Benedicto College Campus, Philippines</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="text-sm">info&#64;benedictocollege.edu.ph</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
              <span class="text-sm">+63 (XXX) XXX-XXXX</span>
            </div>
          </div>
        </div>

        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
          <div class="space-y-2">
            <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
            <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
          </div>
        </div>

        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
          <a href="https://facebook.com/benedictocollege" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Facebook</a>
          <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Website</a>
          <a href="tel:+63321234567" class="text-gray-400 hover:text-green-400 transition duration-300">Phone</a>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>

<script>
  // Enhanced header and about page functionality
  document.addEventListener('DOMContentLoaded', function() {
    // Mobile navigation event handlers
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        const navPane = document.getElementById('mobile-nav-pane');
        const overlay = document.getElementById('mobile-nav-overlay');
        const burgerIcon = document.getElementById('burger-icon');
        const closeIcon = document.getElementById('close-icon');

        if (navPane && !navPane.classList.contains('translate-x-full')) {
          navPane.classList.add('translate-x-full');
          overlay.classList.add('hidden');
          overlay.classList.remove('show');
          burgerIcon.classList.remove('hidden');
          closeIcon.classList.add('hidden');
          document.body.style.overflow = '';
        }
      }
    });

    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768) {
        const navPane = document.getElementById('mobile-nav-pane');
        const overlay = document.getElementById('mobile-nav-overlay');
        const burgerIcon = document.getElementById('burger-icon');
        const closeIcon = document.getElementById('close-icon');

        if (navPane) {
          navPane.classList.add('translate-x-full');
          overlay.classList.add('hidden');
          overlay.classList.remove('show');
          burgerIcon.classList.remove('hidden');
          closeIcon.classList.add('hidden');
          document.body.style.overflow = '';
        }
      }
    });

    // Scroll animations and intersection observer
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');

          // Trigger counter animation for stats
          if (entry.target.classList.contains('stats-section')) {
            animateCounters();
          }
        }
      });
    }, observerOptions);

    // Observe timeline items
    document.querySelectorAll('.timeline-item').forEach(item => {
      observer.observe(item);
    });

    // Observe feature cards
    document.querySelectorAll('.feature-card').forEach(card => {
      observer.observe(card);
    });

    // Observe stats section
    const statsSection = document.querySelector('.stats-section');
    if (statsSection) {
      observer.observe(statsSection);
    }

    // Animated counter function
    function animateCounters() {
      const counters = document.querySelectorAll('.stat-number[data-target]');

      counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;

        const updateCounter = () => {
          if (current < target) {
            current += increment;
            counter.textContent = Math.floor(current).toLocaleString() + '+';
            requestAnimationFrame(updateCounter);
          } else {
            counter.textContent = target.toLocaleString() + '+';
          }
        };

        updateCounter();
      });
    }

    // Smooth scrolling for anchor links - Enhanced version
    function setupSmoothScrolling() {
      const anchors = document.querySelectorAll('a[href^="#"]');
      console.log('Found anchor links:', anchors.length);

      anchors.forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          console.log('Anchor clicked:', this.getAttribute('href'));
          e.preventDefault();

          const targetId = this.getAttribute('href');
          const target = document.querySelector(targetId);

          if (target) {
            console.log('Target found:', target);
            // Simple scroll to target
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          } else {
            console.log('Target not found for:', targetId);
          }
        });
      });
    }

    // Call the function
    setupSmoothScrolling();

    // Also set up a direct click handler for the specific arrow
    const campusArrow = document.querySelector('a[href="#campus-life"]');
    if (campusArrow) {
      console.log('Campus arrow found');
      campusArrow.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Campus arrow clicked');
        const campusSection = document.getElementById('campus-life');
        if (campusSection) {
          console.log('Campus section found, scrolling...');
          campusSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    }

    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
      const scrolled = window.pageYOffset;
      const parallaxElements = document.querySelectorAll('.parallax-element');

      parallaxElements.forEach(element => {
        const speed = element.dataset.speed || 0.5;
        const yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });
    });

    // Enhanced timeline marker interactions
    document.querySelectorAll('.timeline-marker').forEach(marker => {
      marker.addEventListener('mouseenter', function() {
        this.style.transform = 'translate(-50%, -50%) scale(1.5)';
        this.style.boxShadow = '0 0 20px rgba(249, 115, 22, 0.6)';
      });

      marker.addEventListener('mouseleave', function() {
        this.style.transform = 'translate(-50%, -50%) scale(1)';
        this.style.boxShadow = '';
      });
    });

    // Add loading states and progressive enhancement
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (!img.complete) {
        img.classList.add('skeleton');
        img.addEventListener('load', function() {
          this.classList.remove('skeleton');
        });
      }
    });
  });

  // Interactive feature card toggle function
  function toggleFeatureDetails(card) {
    const details = card.querySelector('.feature-details');
    const isHidden = details.classList.contains('hidden');

    // Close all other open details
    document.querySelectorAll('.feature-details').forEach(detail => {
      if (detail !== details) {
        detail.classList.add('hidden');
        detail.parentElement.classList.remove('expanded');
      }
    });

    // Toggle current details
    if (isHidden) {
      details.classList.remove('hidden');
      card.classList.add('expanded');
      details.style.animation = 'fadeInUp 0.3s ease-out';
    } else {
      details.classList.add('hidden');
      card.classList.remove('expanded');
    }
  }

  // Enhanced video handling
  document.addEventListener('DOMContentLoaded', function() {
    const video = document.querySelector('.hero-section video');
    if (video) {
      // Pause video when not in viewport to save bandwidth
      const videoObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            video.play();
          } else {
            video.pause();
          }
        });
      });
      videoObserver.observe(video);

      // Handle video loading errors
      video.addEventListener('error', function() {
        console.log('Video failed to load, showing fallback image');
        this.style.display = 'none';
      });
    }

    // Enhanced interactions for new sections
    // Testimonial card interactions
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    testimonialCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.02)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
      });
    });

    // Leadership card interactions
    const leaderCards = document.querySelectorAll('.leader-card');
    leaderCards.forEach(card => {
      card.addEventListener('click', function() {
        // Add click effect
        this.style.transform = 'scale(0.98)';
        setTimeout(() => {
          this.style.transform = '';
        }, 150);
      });
    });

    // Accreditation card interactions with ripple effect
    const accreditationCards = document.querySelectorAll('.accreditation-card');
    accreditationCards.forEach(card => {
      card.addEventListener('click', function(event) {
        // Add ripple effect
        const ripple = document.createElement('div');
        ripple.style.cssText = `
          position: absolute;
          border-radius: 50%;
          background: rgba(249, 115, 22, 0.3);
          transform: scale(0);
          animation: ripple 0.6s linear;
          pointer-events: none;
        `;

        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (event.clientX - rect.left - size / 2) + 'px';
        ripple.style.top = (event.clientY - rect.top - size / 2) + 'px';

        this.style.position = 'relative';
        this.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });

    // Campus visual interactions
    const campusItems = document.querySelectorAll('.campus-item');
    campusItems.forEach(item => {
      item.addEventListener('mouseenter', function() {
        const img = this.querySelector('img');
        if (img) {
          img.style.transform = 'scale(0.92)';
        }
      });

      item.addEventListener('mouseleave', function() {
        const img = this.querySelector('img');
        if (img) {
          img.style.transform = 'scale(1)';
        }
      });
    });

    // Stats counter animation
    const statItems = document.querySelectorAll('.stat-item');
    const observerOptions = {
      threshold: 0.5,
      rootMargin: '0px 0px -50px 0px'
    };

    const statsObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const statNumber = entry.target.querySelector('.text-3xl');
          if (statNumber && !statNumber.classList.contains('animated')) {
            statNumber.classList.add('animated');
            animateCounter(statNumber);
          }
        }
      });
    }, observerOptions);

    statItems.forEach(item => {
      statsObserver.observe(item);
    });

    function animateCounter(element) {
      const target = element.textContent;
      const isPercentage = target.includes('%');
      const isRatio = target.includes(':');
      const isPlusSign = target.includes('+');

      let finalNumber;
      if (isPercentage) {
        finalNumber = parseInt(target.replace('%', ''));
      } else if (isRatio) {
        finalNumber = parseInt(target.split(':')[0]);
      } else if (isPlusSign) {
        finalNumber = parseInt(target.replace('+', ''));
      } else {
        finalNumber = parseInt(target);
      }

      let current = 0;
      const increment = finalNumber / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= finalNumber) {
          current = finalNumber;
          clearInterval(timer);
        }

        let displayValue = Math.floor(current);
        if (isPercentage) {
          element.textContent = displayValue + '%';
        } else if (isRatio) {
          element.textContent = displayValue + ':1';
        } else if (isPlusSign) {
          element.textContent = displayValue + '+';
        } else {
          element.textContent = displayValue;
        }
      }, 30);
    }

    // Add ripple animation CSS
    const style = document.createElement('style');
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  });
</script>


