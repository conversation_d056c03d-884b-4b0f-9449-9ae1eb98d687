<div class="min-h-screen bg-white text-gray-800 flex flex-col overflow-x-hidden">
  <!-- Enhanced professional header with quick access and search functionality -->
  <header class="bg-gray-950 shadow-xl relative z-10 w-full overflow-x-hidden">
    <div class="container mx-auto px-4 sm:px-6 py-2 sm:py-3 max-w-full">
      <!-- Main header row -->
      <div class="flex justify-between items-center w-full min-w-0">
        <!-- Logo section -->
        <div class="flex-shrink-0 flex justify-start items-center min-w-0">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-8 sm:h-10 md:h-12 lg:h-14 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Enhanced Navigation with Hover Text Labels -->
        <div class="flex items-center justify-end ml-auto space-x-1">
          <!-- Desktop Navigation - Hidden on mobile -->
          <div class="hidden md:flex items-center space-x-1">
            <strong class="text-gray-300 text-sm">LMS 2026</strong>
          </div>

          <!-- Main Website -->
          <a href="https://benedictocollege.edu.ph" target="_blank" class="group relative hidden md:flex items-center cursor-pointer">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
            </svg>
            <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">Main Website</span>
          </a>

          <!-- Help & Support -->
          <a routerLink="/support" class="group relative hidden md:flex items-center cursor-pointer">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">Help & Support</span>
          </a>

          <!-- About Us -->
          <a routerLink="/about" class="group relative hidden md:flex items-center cursor-pointer">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">About Us</span>
          </a>

          <!-- Contact Us -->
          <a routerLink="/contact" class="group relative hidden md:flex items-center cursor-pointer">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">Contact Us</span>
          </a>

          <!-- My Account -->
          <a routerLink="/login" class="group relative hidden md:flex items-center cursor-pointer">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="absolute bottom-[-35px] left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">My Account</span>
          </a>
        </div>

        <!-- Mobile Burger Menu Button -->
        <div class="md:hidden flex items-center">
          <button class="text-white focus:outline-none p-2 relative z-50">
            <svg class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
            </svg>
          </button>
        </div>

      </div>
    </div>
  </header>

  <!-- Mobile Overlay for blur effect -->
  <div id="mobile-nav-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300 md:hidden" onclick="
    const pane = document.getElementById('mobile-nav-pane');
    const burger = document.getElementById('burger-icon');
    const close = document.getElementById('close-icon');
    const overlayEl = document.getElementById('mobile-nav-overlay');

    pane.classList.add('translate-x-full');
    overlayEl.classList.add('hidden');
    overlayEl.classList.remove('show');
    burger.classList.remove('hidden');
    close.classList.add('hidden');
    document.body.style.overflow = '';
  "></div>



  <!-- Mobile Sliding Navigation Pane -->
  <div id="mobile-nav-pane" class="fixed top-0 right-0 h-full w-80 bg-gray-900 transform translate-x-full transition-transform duration-300 ease-in-out z-50 md:hidden">
    <!-- Navigation Header -->
    <div class="bg-gray-950 border-b border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <img src="assets/images/BcLogo.png" alt="BC Logo" class="h-8 w-auto mr-3">
          <span class="text-white font-bold text-lg">Navigation</span>
        </div>
        <button id="close-nav-button" class="text-white hover:text-orange-400 transition duration-300" onclick="
          const closePane = document.getElementById('mobile-nav-pane');
          const closeBurger = document.getElementById('burger-icon');
          const closeX = document.getElementById('close-icon');
          const closeOverlay = document.getElementById('mobile-nav-overlay');

          closePane.classList.add('translate-x-full');
          closeOverlay.classList.add('hidden');
          closeOverlay.classList.remove('show');
          closeBurger.classList.remove('hidden');
          closeX.classList.add('hidden');
          document.body.style.overflow = '';
        ">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Navigation Content -->
    <div class="p-6 h-full overflow-y-auto">
      <!-- Main Navigation -->
      <div class="space-y-1 mb-6">
        <h3 class="text-gray-400 text-xs uppercase tracking-wider font-semibold mb-3">Main Navigation</h3>
        <a href="https://benedictocollege.edu.ph" target="_blank" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
          </svg>
          <span class="font-medium">Main Website</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
          </svg>
        </a>
        <a routerLink="/about" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">About Us</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
        <a routerLink="/contact" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
          <span class="font-medium">Contact Us</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
        <a routerLink="/support" class="nav-link flex items-center py-3 px-4 text-white hover:bg-gray-800 rounded-lg transition duration-300 group">
          <svg class="w-5 h-5 mr-3 text-orange-400 group-hover:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">Help & Support</span>
          <svg class="w-4 h-4 ml-auto text-gray-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>

      <!-- Login Section -->
      <div class="border-t border-gray-700 pt-6">
        <h3 class="text-gray-400 text-xs uppercase tracking-wider font-semibold mb-3">Account Access</h3>
        <a routerLink="/login" class="nav-link flex items-center py-3 px-4 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition duration-300 group mb-3">
          <svg class="w-5 h-5 mr-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          <span class="font-medium">Student Login</span>
          <svg class="w-4 h-4 ml-auto text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>

        <!-- Quick Access Portals -->
        <div class="space-y-2">
          <a routerLink="/facultylogin" class="nav-link flex items-center py-2 px-4 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition duration-300 text-sm">
            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
            Faculty Portal
          </a>
          <a routerLink="/adminlogin" class="nav-link flex items-center py-2 px-4 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition duration-300 text-sm">
            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
            Admin Portal
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Hero section ni diri, mao ni ang main attraction sa page. Gi-butang nako diri ang mga background images nga nag-slideshow ug ang main content. Nindot kaayo ni tan-awon especially ang tethering animation sa LOGIN button -->
  <main class="flex-grow relative overflow-hidden w-full">
    <!-- Diri nako gi-setup ang background slideshow, naa ni siyay tatlong images nga nag-rotate. Gi-absolute position nako ni para ma-overlay sa background -->
    <div class="absolute inset-0 z-0 ">
      <div class="background-slideshow w-full h-full bg-white-950 shadow-xl border-b-6 border-white-500 relative z-10 ">
        <div class="slide active" style="background-image: url('https://i.imgur.com/a7dTugL.jpeg');"></div>
        <div class="slide" style="background-image: url('https://i.imgur.com/O2oKst2.png');"></div>
        <div class="slide" style="background-image: url('https://i.imgur.com/YPE0QjQ.jpeg');"></div>
      </div>
    </div>

    <!-- Gi-overlay nako ni nga black para ma-readable ang text sa ibabaw. Kung wala ni, dili makita ang text kay bright kaayo ang background images -->
    <div class="absolute inset-0 bg-black bg-opacity-30 z-10"></div>

    <!-- Main content sa hero section - improved mobile responsiveness -->
    <div class="container mx-auto px-6 sm:px-6 py-12 sm:py-16 text-center flex flex-col justify-center items-center min-h-[calc(100vh-80px)] sm:min-h-[calc(100vh-128px)] relative z-20">
      <!-- Main content - responsive sizing and spacing -->
      <div class="flex-1 flex flex-col justify-center items-center w-full max-w-3xl mx-auto space-y-6 sm:space-y-8">
        <h1 class="text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white drop-shadow-lg leading-tight text-center">
          <span class="block sm:hidden">LIBRARY MANAGEMENT SYSTEM</span>
          <span class="hidden sm:block">LIBRARY MANAGEMENT <br>SYSTEM</span>
        </h1>
        <p class="text-xs xs:text-sm sm:text-base md:text-lg text-gray-200 drop-shadow-md max-w-2xl mx-auto px-2 sm:px-4 text-center leading-relaxed">
          Your gateway to a world of knowledge and academic excellence.
        </p>
        <div class="flex justify-center items-center w-full">
          <a routerLink="/login" class="bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-6 sm:py-3 sm:px-8 rounded-full transition duration-300 login-tether border-2 border-white shadow-lg text-sm sm:text-base">
            LOGIN
          </a>
        </div>
      </div>

      <!-- Bottom section - responsive spacing -->
      <div class="flex flex-col items-center space-y-4 sm:space-y-6 w-full mt-8 sm:mt-12">
        <!-- Learn More button - responsive sizing -->
        <a href="#about" class="border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-2 px-6 sm:py-3 sm:px-8 rounded-full transition duration-300 shadow-lg backdrop-blur-sm bg-white/10 text-sm sm:text-base">
          Learn More
        </a>

        <!-- Scroll indicator - responsive sizing -->
        <div class="text-center">
          <a href="#about" class="text-gray-300 hover:text-white">
            <svg class="w-6 h-6 sm:w-8 sm:h-8 animate-bounce drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </main>

    <section id="about" class="py-12 sm:py-16 md:py-20 bg-gradient-to-br from-indigo-50 via-white to-orange-50 relative overflow-hidden">
    <!-- Animated Background Elements - responsive positioning -->
    <div class="absolute inset-0 opacity-20 sm:opacity-30">
      <div class="absolute top-10 sm:top-20 left-4 sm:left-10 w-16 sm:w-24 md:w-32 h-16 sm:h-24 md:h-32 bg-gradient-to-br from-orange-300 to-orange-400 rounded-full animate-pulse opacity-40"></div>
      <div class="absolute bottom-10 sm:bottom-20 right-4 sm:right-10 w-12 sm:w-16 md:w-24 h-12 sm:h-16 md:h-24 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full animate-pulse opacity-40" style="animation-delay: 1s;"></div>
      <div class="absolute top-1/2 left-1/4 w-8 sm:w-12 md:w-16 h-8 sm:h-12 md:h-16 bg-gradient-to-br from-indigo-300 to-indigo-400 rounded-full animate-pulse opacity-30" style="animation-delay: 2s;"></div>
    </div>

    <div class="container mx-auto px-6 sm:px-6 relative z-10">
      <!-- Section Header - responsive spacing and sizing -->
      <div class="text-center mb-8 sm:mb-12 md:mb-16">
        <div class="inline-block mb-4 sm:mb-6">
          <div class="bg-gradient-to-r from-indigo-500 to-orange-500 rounded-full p-1 shadow-xl">
            <div class="bg-white rounded-full px-4 sm:px-6 md:px-8 py-2 sm:py-3">
              <span class="text-xs sm:text-sm font-bold text-black uppercase tracking-wider">About Our Institution</span>
            </div>
          </div>
        </div>
        <h2 class="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-black mb-4 sm:mb-6 leading-tight px-2 sm:px-4">
          About Benedicto College
        </h2>
        <div class="max-w-4xl mx-auto px-2 sm:px-4">
          <p class="text-base sm:text-lg md:text-xl text-gray-700 leading-relaxed mb-6 sm:mb-8 text-center">
            Benedicto College is a premier educational institution committed to academic excellence and holistic development.
            Our Library Management System represents our dedication to providing cutting-edge technology and resources to support
            our students, faculty, and staff in their pursuit of knowledge and academic success.
          </p>
          <div class="flex items-center justify-center">
            <div class="w-16 sm:w-24 h-1 bg-gradient-to-r from-indigo-500 to-orange-500 rounded-full"></div>
          </div>
        </div>
      </div>
    </div>


 
    <div class="flex flex-col items-center space-y-6">
  <div class="text-center">
    <a href="#features" class="text-black hover:text-white">
      <svg class="w-8 h-8 animate-bounce drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </a>
  </div>
</div>




  </section>

  <!-- Mission section ni, diri nako gi-showcase ang Benedicto College mission ug vision. Naa pay mga floating elements ug gradient backgrounds para nindot tan-awon. Gi-separate nako ni sa hero section -->
  <section id="features" class="bg-gradient-to-br from-orange-100 via-orange-50 to-gray-100 py-20 relative overflow-hidden">
    <!-- Background pattern ni para nindot ang design, gi-absolute position nako ni ug gi-set ang opacity para dili kaayo obvious pero naa gihapon effect -->
    <div class="absolute inset-0 opacity-15">
      <div class="absolute top-10 left-10 w-32 h-32 bg-orange-400 rounded-full"></div>
      <div class="absolute top-40 right-20 w-24 h-24 bg-orange-500 rounded-full"></div>
      <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-orange-300 rounded-full"></div>
      <div class="absolute bottom-40 right-1/3 w-28 h-28 bg-orange-600 rounded-full"></div>
    </div>
    <div class="container mx-auto px-6 relative z-10">
      <!-- Mission statement section ni, diri nako gi-highlight ang Benedicto College branding. Gi-use nako ang blue colors para professional tan-awon ug gi-add nako ang grid pattern sa background -->
      <div class="w-full mb-16">
        <div class="bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 py-20 relative overflow-hidden">
          <!-- Elegant background pattern using SVG grid, gi-set nako ni nga subtle lang para dili overwhelming pero naa gihapon visual interest -->
          <div class="absolute inset-0 opacity-30">
            <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#3b82f6" stroke-width="0.5" opacity="0.3"/>
                </pattern>
              </defs>
              <rect width="100" height="100" fill="url(#grid)" />
            </svg>
          </div>


          <!-- Floating elements nga nag-animate, gi-butang nako ni para ma-lively ang design. Naa pay different animation delays para dili sabay-sabay ang movement -->
          <div class="absolute top-10 right-10 w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full opacity-20 animate-pulse"></div>
          <div class="absolute bottom-10 left-10 w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>
          <div class="absolute top-1/2 right-1/4 w-12 h-12 bg-gradient-to-br from-indigo-400 to-indigo-500 rounded-full opacity-15 animate-pulse" style="animation-delay: 2s;"></div>

          <div class="container mx-auto px-6 relative z-10">
            <!-- Professional header section para sa mission statement, gi-center nako ni tanan ug gi-add ang excellence badge para nindot -->
            <div class="text-center mb-16">
              <!-- Excellence badge ni, gi-design nako ni nga nindot ug professional. Naa pay gradient dots sa sides para ma-emphasize -->
              <div class="inline-block mb-8">
                <div class="bg-white rounded-full shadow-lg border border-gray-200 px-8 py-4">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-gradient-to-r from-orange-500 to-blue-500 rounded-full"></div>
                    <span class="text-sm font-semibold text-gray-700 uppercase tracking-wider">Excellence in Education</span>
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-orange-500 rounded-full"></div>
                  </div>
                </div>
              </div>

              <!-- Main title sa Benedicto College, gi-gradient nako ang text para nindot ug gi-make nako ni nga responsive sa different screen sizes -->
            <h2 class="text-5xl md:text-7xl font-bold text-orange-500 mb-8 leading-tight">
  BENEDICTO COLLEGE
</h2>
  



              <!-- Subtitle with Professional Styling -->
              <div class="max-w-2xl mx-auto">
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h3 class="text-3xl font-bold text-blue-600 mb-4">
                    Your Education… Our Mission
                  </h3>
                  <div class="flex items-center justify-center mb-6">
                    <div class="w-20 h-1 bg-gradient-to-r from-orange-500 to-blue-500 rounded-full"></div>
                  </div>
                  <p class="text-lg text-gray-700 leading-relaxed">
                    As the preferred higher educational institution in the Asia-Pacific, Benedicto College will be a globally competitive institution and a catalyst in nation-building, creating a better quality of life and developing productive members of the society.
                  </p>
                </div>
              </div>

            </div>

  

                            <div class="flex flex-col items-center space-y-6">
  <div class="text-center">
    <a href="#excellence" class="text-black hover:text-white">
      <svg class="w-8 h-8 animate-bounce drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </a>
  </div>
</div>


            <!-- Professional Feature Cards -->
            <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <!-- Excellence Card -->
              <div class="group bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                <!-- Professional Stock Image for Excellence -->
                <div class="relative h-48 overflow-hidden">
                  <img
                    src="https://scontent.fceb6-3.fna.fbcdn.net/v/t39.30808-6/494197134_1279438467524302_193824388238257702_n.jpg?stp=cp6_dst-jpg_tt6&_nc_cat=104&ccb=1-7&_nc_sid=833d8c&_nc_eui2=AeHc-TLAr7XI2yFLf9kL216Vh5fzEBBYnCiHl_MQEFicKPkFGFRnA0AHHs99vvQx7YrJ2Fa5v7MqUqOIpBvUrvUC&_nc_ohc=D-IWKfELDjgQ7kNvwHXC3D0&_nc_oc=AdlLDBgywWRFiOYIRGf1ulEzKELzFa3rtixss0YC5MvP9smBdg5esnrWIWGmBJU8UY5YlkabFkbHXj0nPx7BXIYS&_nc_zt=23&_nc_ht=scontent.fceb6-3.fna&_nc_gid=Ff1CMCZoaQPNnZNV38lTcg&oh=00_AfQoEStKoSxBXJ2YrobPWgD99WZMI1FB59sC2pwBwEDR4w&oe=6888128F"
                    alt="Academic Excellence - Students studying in modern library"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  >
                  <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div class="absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <div class="p-8 text-center">
                  <h4 class="text-2xl font-bold text-gray-900 mb-3">Excellence</h4>
                  <p class="text-gray-600 leading-relaxed">Maintaining the highest academic standards and fostering a culture of continuous improvement in all educational endeavors.</p>
                </div>
              </div>

              <!-- Innovation Card -->
              <div class="group bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                <!-- Professional Stock Image for Innovation -->
                <div class="relative h-48 overflow-hidden">
                  <img
                    src="assets/images/innovation.jpg"
                    alt="Innovation - Modern technology and digital learning"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  >
                  <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div class="absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"></path>
                    </svg>
                  </div>
                </div>
                <div class="p-8 text-center">
                  <h4 class="text-2xl font-bold text-gray-900 mb-3">Innovation</h4>
                  <p class="text-gray-600 leading-relaxed">Embracing cutting-edge technology and modern teaching methodologies to prepare students for tomorrow's challenges.</p>
                </div>
              </div>

              <!-- Community Card -->
              <div class="group bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-500">
                <!-- Professional Stock Image for Community -->
                <div class="relative h-48 overflow-hidden">
                  <img
                    src="https://itreseller.pl/wp-content/uploads/2022/03/Accenture-MWC_2022.jpg"
                    alt="Community - Students collaborating and studying together in library"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  >
                  <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div class="absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                  </div>
                </div>
                <div class="p-8 text-center">
                  <h4 class="text-2xl font-bold text-gray-900 mb-3">Community</h4>
                  <p class="text-gray-600 leading-relaxed">Building strong connections and partnerships that create lasting positive impact in our local and global communities.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      

      <!-- Features Section -->
      <div id= "excellence" class="bg-gradient-to-br from-orange-50 via-white to-blue-50 py-20 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-20">
          <svg class="w-full h-full" viewBox="0 0 60 60" preserveAspectRatio="none">
            <defs>
              <pattern id="hexagon" width="60" height="60" patternUnits="userSpaceOnUse">
                <polygon points="30,5 50,20 50,40 30,55 10,40 10,20" fill="none" stroke="#f97316" stroke-width="1" opacity="0.3"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#hexagon)" />
          </svg>
        </div>

        <div class="container mx-auto px-6 relative z-10">
          <!-- Section Header -->
          <div class="text-center mb-16">
            <div class="inline-block mb-6">
              <div class="bg-gradient-to-r from-orange-500 to-blue-500 rounded-full p-1 shadow-lg">
                <div class="bg-white rounded-full px-8 py-3">
                  <span class="text-sm font-bold text-black uppercase tracking-wider">Our Excellence</span>
                </div>
              </div>
            </div>
            <h2 class="text-4xl md:text-5xl font-bold text-black mb-6">
              Library Management Excellence
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Supporting our educational mission with cutting-edge library technology and innovative learning solutions
            </p>
          </div>

          <!-- Feature Cards -->
          <div class="grid md:grid-cols-3 gap-10">
            <!-- Academic Excellence Card -->
            <div class="group relative overflow-hidden">
              <div class="absolute inset-0 bg-gradient-to-r from-gray-400 to-gray-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>

              <!-- Academic Pattern Background -->
              <div class="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-500">
                <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                  <defs>
                    <pattern id="academic-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
                      <rect width="20" height="20" fill="none"/>
                      <path d="M10,2 L18,10 L10,18 L2,10 Z" fill="#374151" opacity="0.3"/>
                      <circle cx="10" cy="10" r="1" fill="#374151"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#academic-pattern)"/>
                </svg>
              </div>

              <div class="relative bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:-translate-y-3 transition-all duration-500 overflow-hidden">
                <!-- Academic Excellence Image -->
                <div class="relative h-48 -mx-8 -mt-8 mb-6 overflow-hidden rounded-t-3xl">
                  <img
                    src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="Academic Excellence - University students studying"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy">
                  <div class="absolute inset-0 bg-gradient-to-t from-gray-900/60 via-gray-900/20 to-transparent"></div>
                  <div class="absolute bottom-4 left-4 text-white">
                    <div class="text-sm font-semibold">Academic Excellence</div>
                    <div class="text-xs opacity-90">Graduation Success</div>
                  </div>
                </div>
                <div class="text-center">
                  <!-- Enhanced Floating Icon with Progress Ring -->
                  <div class="relative mb-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 relative overflow-hidden">
                      <!-- Animated background pulse -->
                      <div class="absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-800 rounded-2xl animate-pulse opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
                      <svg class="w-10 h-10 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                      </svg>
                    </div>
                   
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-gray-600 rounded-full animate-ping"></div>
                  </div>

                  <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors duration-300">Academic Excellence</h3>
                  <p class="text-gray-600 leading-relaxed mb-4">Comprehensive programs designed to develop globally competitive graduates with strong academic foundation and practical skills for the modern world.</p>

                  <!-- Interactive Metrics -->
                  <div class="opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-500 delay-200">
                    <div class="grid grid-cols-2 gap-4 mt-6 pt-4 border-t border-gray-100">
                      <div class="text-center">
                        <div class="text-2xl font-bold text-gray-800">87%</div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">Employment Rate</div>
                      </div>
                      <div class="text-center">
                        <div class="text-2xl font-bold text-gray-800">8</div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">Colleges</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Global Perspective Card -->
            <div class="group relative overflow-hidden">
              <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>

              <!-- Global Pattern Background -->
              <div class="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-500">
                <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                  <defs>
                    <pattern id="global-pattern" width="25" height="25" patternUnits="userSpaceOnUse">
                      <rect width="25" height="25" fill="none"/>
                      <circle cx="12.5" cy="12.5" r="8" fill="none" stroke="#3b82f6" stroke-width="0.5" opacity="0.4"/>
                      <path d="M4.5,12.5 Q12.5,4.5 20.5,12.5 Q12.5,20.5 4.5,12.5" fill="none" stroke="#3b82f6" stroke-width="0.3"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#global-pattern)"/>
                </svg>
              </div>

              <div class="relative bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:-translate-y-3 transition-all duration-500 overflow-hidden">
                <!-- Global Perspective Image -->
                <div class="relative h-48 -mx-8 -mt-8 mb-6 overflow-hidden rounded-t-3xl">
                  <img
                    src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="Global Perspective - International business meeting"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy">
                  <div class="absolute inset-0 bg-gradient-to-t from-blue-900/60 via-blue-900/20 to-transparent"></div>
                  <div class="absolute bottom-4 left-4 text-white">
                    <div class="text-sm font-semibold">Global Perspective</div>
                    <div class="text-xs opacity-90">International Partnerships</div>
                  </div>
                </div>
                <div class="text-center">
                  <!-- Enhanced Floating Icon with Orbital Animation -->
                  <div class="relative mb-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 relative overflow-hidden">
                      <!-- Animated orbital rings -->
                      <div class="absolute inset-0 rounded-2xl">
                        <div class="absolute inset-2 border border-blue-300 rounded-xl opacity-0 group-hover:opacity-40 group-hover:animate-spin transition-all duration-1000" style="animation-duration: 3s;"></div>
                        <div class="absolute inset-4 border border-blue-200 rounded-lg opacity-0 group-hover:opacity-30 group-hover:animate-spin transition-all duration-1000" style="animation-duration: 2s; animation-direction: reverse;"></div>
                      </div>
                      <svg class="w-10 h-10 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                      </svg>
                    </div>
                    <!-- Expanding Circles -->
                    <div class="absolute inset-0 flex items-center justify-center">
                      <div class="w-24 h-24 border-2 border-blue-200 rounded-full opacity-0 group-hover:opacity-30 group-hover:scale-150 transition-all duration-1000"></div>
                      <div class="absolute w-28 h-28 border border-blue-100 rounded-full opacity-0 group-hover:opacity-20 group-hover:scale-150 transition-all duration-1000 delay-200"></div>
                    </div>
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-400 rounded-full animate-ping"></div>
                  </div>

                  <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-800 transition-colors duration-300">Global Perspective</h3>
                  <p class="text-gray-600 leading-relaxed mb-4">International partnerships and exchange programs preparing students for success in the Asia-Pacific region and beyond the global marketplace.</p>

                  <!-- Interactive Global Metrics -->
                  <div class="opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-500 delay-200">
                    <div class="grid grid-cols-2 gap-4 mt-6 pt-4 border-t border-gray-100">
                      <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">15+</div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">LGU Partners</div>
                      </div>
                      <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">2K+</div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">TechVoc Grads</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Community Impact Card -->
            <div class="group relative overflow-hidden">
              <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-indigo-500 rounded-3xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>

              <!-- Community Pattern Background -->
              <div class="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-500">
                <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                  <defs>
                    <pattern id="community-pattern" width="30" height="30" patternUnits="userSpaceOnUse">
                      <rect width="30" height="30" fill="none"/>
                      <circle cx="15" cy="8" r="3" fill="#6366f1" opacity="0.3"/>
                      <circle cx="8" cy="20" r="2.5" fill="#6366f1" opacity="0.25"/>
                      <circle cx="22" cy="20" r="2.5" fill="#6366f1" opacity="0.25"/>
                      <path d="M8,22 Q15,18 22,22" fill="none" stroke="#6366f1" stroke-width="0.5" opacity="0.3"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#community-pattern)"/>
                </svg>
              </div>

              <div class="relative bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:-translate-y-3 transition-all duration-500 overflow-hidden">
                <!-- Community Impact Image -->
                <div class="relative h-48 -mx-8 -mt-8 mb-6 overflow-hidden rounded-t-3xl">
                  <img
                    src="https://images.unsplash.com/photo-1559027615-cd4628902d4a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="Community Impact - Volunteers helping community"
                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy">
                  <div class="absolute inset-0 bg-gradient-to-t from-indigo-900/60 via-indigo-900/20 to-transparent"></div>
                  <div class="absolute bottom-4 left-4 text-white">
                    <div class="text-sm font-semibold">Community Impact</div>
                    <div class="text-xs opacity-90">Outreach Programs</div>
                  </div>
                </div>
                <div class="text-center">
                  <!-- Enhanced Floating Icon with Network Animation -->
                  <div class="relative mb-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 relative overflow-hidden">
                      <!-- Network connection lines -->
                      <div class="absolute inset-0">
                        <div class="absolute top-2 left-2 w-1 h-1 bg-indigo-300 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-500 delay-100"></div>
                        <div class="absolute top-2 right-2 w-1 h-1 bg-indigo-300 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-500 delay-200"></div>
                        <div class="absolute bottom-2 left-2 w-1 h-1 bg-indigo-300 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-500 delay-300"></div>
                        <div class="absolute bottom-2 right-2 w-1 h-1 bg-indigo-300 rounded-full opacity-0 group-hover:opacity-60 transition-all duration-500 delay-400"></div>
                        <!-- Connection lines -->
                        <svg class="absolute inset-0 w-full h-full opacity-0 group-hover:opacity-40 transition-opacity duration-500">
                          <line x1="8" y1="8" x2="72" y2="8" stroke="#a5b4fc" stroke-width="0.5"/>
                          <line x1="8" y1="72" x2="72" y2="72" stroke="#a5b4fc" stroke-width="0.5"/>
                          <line x1="8" y1="8" x2="8" y2="72" stroke="#a5b4fc" stroke-width="0.5"/>
                          <line x1="72" y1="8" x2="72" y2="72" stroke="#a5b4fc" stroke-width="0.5"/>
                        </svg>
                      </div>
                      <svg class="w-10 h-10 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                    </div>
                    <!-- Ripple Effect -->
                    <div class="absolute inset-0 flex items-center justify-center">
                      <div class="w-20 h-20 border-2 border-indigo-200 rounded-2xl opacity-0 group-hover:opacity-40 group-hover:scale-125 transition-all duration-700"></div>
                      <div class="absolute w-24 h-24 border border-indigo-100 rounded-2xl opacity-0 group-hover:opacity-30 group-hover:scale-125 transition-all duration-700 delay-150"></div>
                    </div>
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-indigo-400 rounded-full animate-ping"></div>
                  </div>

                  <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-indigo-800 transition-colors duration-300">Community Impact</h3>
                  <p class="text-gray-600 leading-relaxed mb-4">Developing productive members of society through community engagement, leadership development, and social responsibility initiatives.</p>

                  <!-- Interactive Community Metrics -->
                  <div class="opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-500 delay-200">
                    <div class="grid grid-cols-2 gap-4 mt-6 pt-4 border-t border-gray-100">
                      <div class="text-center">
                        <div class="text-2xl font-bold text-indigo-600">12+</div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">Municipalities</div>
                      </div>
                      <div class="text-center">
                        <div class="text-2xl font-bold text-indigo-600">2K+</div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">Trainees</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>

    <div class="flex flex-col items-center space-y-6">
  <div class="text-center">
    <a href="#about" class="text-black hover:text-white">
      <svg class="w-8 h-8 animate-bounce drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
      </svg>
    </a>
  </div>
</div>

  </section>

  <!-- About Section -->


  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500">
    <div class="container mx-auto px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span>library&#64;benedictocollege.edu.ph</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>A.S. Fortuna Street, Mandaue City</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4 mb-4">
              <a href="https://facebook.com/benedictocollegeofficial" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Facebook">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Official Website">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="mailto:library&#64;benedictocollege.edu.ph" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Email">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </a>
              <a href="tel:+6332455790" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Call Us">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
            <!-- Operating Hours -->
            <div class="text-sm text-gray-400">
              <div class="flex items-center justify-center md:justify-start mb-1">
                <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-semibold">Library Hours:</span>
              </div>
              <div class="text-xs">Mon-Fri: 7:00 AM - 8:00 PM</div>
              <div class="text-xs">Sat: 8:00 AM - 5:00 PM</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px and beyond) -->
      <div class="hidden xl:block">
        <div class="flex justify-between items-start mb-8">
          <!-- Get in Touch Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Get in Touch</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span>library&#64;benedictocollege.edu.ph</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>A.S. Fortuna Street, Mandaue City</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex space-x-4 mb-4">
              <a href="https://facebook.com/benedictocollegeofficial" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Facebook">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Official Website">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="mailto:library&#64;benedictocollege.edu.ph" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Email">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </a>
              <a href="tel:+6332455790" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110" title="Call Us">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
            <!-- Operating Hours -->
            <div class="text-sm text-gray-400">
              <div class="flex items-center mb-1">
                <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-semibold">Library Hours:</span>
              </div>
              <div class="text-xs">Mon-Fri: 7:00 AM - 8:00 PM</div>
              <div class="text-xs">Sat: 8:00 AM - 5:00 PM</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll to Top Button -->
  <button
    id="scrollToTopBtn"
    onclick="scrollToTop()"
    class="fixed bottom-6 right-6 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 z-50 opacity-0 invisible"
    title="Scroll to top"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
    </svg>
  </button>
</div>

<script>
  // Scroll to Top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  // Show/hide scroll to top button based on scroll position
  window.addEventListener('scroll', function() {
    const scrollToTopBtn = document.getElementById('scrollToTopBtn');
    if (window.pageYOffset > 300) {
      scrollToTopBtn.classList.remove('opacity-0', 'invisible');
      scrollToTopBtn.classList.add('opacity-100', 'visible');
    } else {
      scrollToTopBtn.classList.remove('opacity-100', 'visible');
      scrollToTopBtn.classList.add('opacity-0', 'invisible');
    }
  });



  // Enhanced header functionality
  document.addEventListener('DOMContentLoaded', function() {

    // Login dropdown functionality
    const loginDropdownButton = document.getElementById('login-dropdown-button');
    const loginDropdown = document.getElementById('login-dropdown');

    if (loginDropdownButton && loginDropdown) {
      loginDropdownButton.addEventListener('click', function(e) {
        e.stopPropagation();
        loginDropdown.classList.toggle('hidden');
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', function(e) {
        if (!loginDropdownButton.contains(e.target) && !loginDropdown.contains(e.target)) {
          loginDropdown.classList.add('hidden');
        }
      });

      // Close dropdown on escape key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          loginDropdown.classList.add('hidden');
        }
      });
    }

    // Search functionality (basic implementation)
    const searchInputs = document.querySelectorAll('input[placeholder*="Search library resources"]');
    searchInputs.forEach(input => {
      input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          const searchTerm = this.value.trim();
          if (searchTerm) {
            // Placeholder for search functionality
            console.log('Searching for:', searchTerm);
            // In a real implementation, this would redirect to a search results page
            // window.location.href = `/search?q=${encodeURIComponent(searchTerm)}`;
          }
        }
      });
    });
  });
</script>
