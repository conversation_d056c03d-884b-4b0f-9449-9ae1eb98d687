/* Contact page specific styles */

/* Header SVG icon styles */
header svg {
  stroke: #000000 !important; /* BLACK color */
  stroke-width: 2 !important;
  fill: none !important;
  color: #000000 !important;
}

/* Header SVG icon hover effects */
.group:hover svg {
  stroke: #fb923c !important; /* Orange-400 color */
  transform: scale(1.1);
}

/* SVG transition effects */
header .group svg {
  transition: all 0.3s ease;
}

/* Mobile: Hide desktop navigation links */
@media (max-width: 768px) {
  #nav-links .group {
    display: none !important;
  }
}
.contact-container {
  max-width: 1200px;
  margin: 0 auto;
}

.contact-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 14px;
  margin-top: 4px;
}

.submit-btn {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  padding: 14px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
}

.contact-info-card {
  background: linear-gradient(135deg, #1f2937, #374151);
  border-radius: 12px;
  padding: 2rem;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.contact-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.contact-info-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.contact-icon {
  width: 24px;
  height: 24px;
  margin-right: 16px;
  color: #f97316;
}

@media (max-width: 768px) {
  .contact-form,
  .contact-info-card {
    padding: 1.5rem;
  }
  
  .form-input {
    padding: 10px 14px;
  }
  
  .submit-btn {
    padding: 12px 24px;
  }
}
